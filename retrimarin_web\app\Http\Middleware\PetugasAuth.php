<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class PetugasAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Debug logging
        Log::info('PetugasAuth Middleware Debug', [
            'url' => $request->url(),
            'method' => $request->method(),
            'is_ajax' => $request->ajax(),
            'expects_json' => $request->expectsJson(),
            'session_id' => session()->getId(),
            'petugas_check' => Auth::guard('petugas')->check(),
            'petugas_user' => Auth::guard('petugas')->user(),
            'all_guards' => [
                'web' => Auth::guard('web')->check(),
                'petugas' => Auth::guard('petugas')->check(),
            ]
        ]);

        if (!Auth::guard('petugas')->check()) {
            Log::warning('Petugas authentication failed', [
                'url' => $request->url(),
                'session_data' => session()->all()
            ]);

            // If it's an AJAX request, return JSON response
            if ($request->expectsJson() || $request->ajax()) {
                return response()->json([
                    'error' => 'Unauthorized',
                    'message' => 'Sesi Anda telah berakhir. Silakan login kembali.'
                ], 401);
            }

            return redirect()->route('petugas.login');
        }

        return $next($request);
    }
}
