<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\OperatorAuthController;
use App\Http\Controllers\Api\LocationController;
use App\Http\Controllers\Api\PetugasAuthController;
use App\Http\Controllers\MidtransWebhookController;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Operator Authentication Routes
Route::prefix('operator')->group(function () {
    // Public routes
    Route::post('/login', [OperatorAuthController::class, 'login']);

    // Protected routes
    Route::middleware('auth:operator')->group(function () {
        Route::get('/profile', [OperatorAuthController::class, 'profile']);
        Route::post('/logout', [OperatorAuthController::class, 'logout']);
        Route::post('/change-password', [OperatorAuthController::class, 'changePassword']);
        Route::put('/update-profile', [OperatorAuthController::class, 'updateProfile']);

        // Location tracking routes
        Route::post('/send-location', [LocationController::class, 'sendLocation']);
        Route::get('/tracking-history', [LocationController::class, 'getTrackingHistory']);
    });
});

// Petugas Authentication Routes
Route::prefix('petugas')->group(function () {
    // Public routes
    Route::post('/login', [PetugasAuthController::class, 'login']);

    // Protected routes
    Route::middleware('auth:petugas-api')->group(function () {
        Route::get('/profile', [PetugasAuthController::class, 'profile']);
        Route::post('/logout', [PetugasAuthController::class, 'logout']);
        Route::post('/change-password', [PetugasAuthController::class, 'changePassword']);
        Route::put('/update-profile', [PetugasAuthController::class, 'updateProfile']);

        // Dashboard and statistics
        Route::get('/dashboard-stats', [PetugasAuthController::class, 'dashboardStats']);

        // Retribusi related routes
        Route::get('/companies', [PetugasAuthController::class, 'getCompanies'])->name('api.companies');
        Route::get('/ships-by-payment-type', [PetugasAuthController::class, 'getShipsByPaymentType'])->name('api.ships.by.payment.type');
        Route::get('/tarifs-by-ship-category', [PetugasAuthController::class, 'getTarifsByShipCategory'])->name('api.tarifs.by.ship.category');
        Route::get('/payment-methods', [PetugasAuthController::class, 'getPaymentMethods'])->name('api.payment.methods');
        Route::post('/retribusi', [PetugasAuthController::class, 'createRetribusi'])->name('api.retribusi.create');
        Route::get('/retribusi-history', [PetugasAuthController::class, 'getRetribusiHistory'])->name('api.retribusi.history');
    });
});

// Midtrans webhook routes (no authentication required)
Route::post('/midtrans/notification', [MidtransWebhookController::class, 'notification']);
Route::post('/midtrans/check-status', [MidtransWebhookController::class, 'checkStatus']);
