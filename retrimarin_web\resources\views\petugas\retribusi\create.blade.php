@extends('petugas.layouts.app')

@section('title', 'Buat Retribusi - ' . config('app.name'))

@section('content')
    <!-- Header -->
    <div class="card bg-light-info shadow-none position-relative overflow-hidden">
        <div class="card-body px-4 py-3">
            <div class="row align-items-center">
                <div class="col-9">
                    <h4 class="fw-semibold mb-8">Buat Retribusi Baru</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a class="text-muted"
                                    href="{{ route('petugas.dashboard') }}">Dashboard</a></li>
                            <li class="breadcrumb-item" aria-current="page">Buat Retribusi</li>
                        </ol>
                    </nav>
                </div>
                <div class="col-3">
                    <div class="text-center mb-n5">
                        <img src="{{ asset('package/dist/images/breadcrumb/ChatBc.png') }}" alt=""
                            class="img-fluid mb-n4">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <h5 class="card-title fw-semibold">Form Retribusi</h5>
            <p class="card-subtitle mb-4">Isi form di bawah untuk membuat retribusi baru</p>

            <form action="{{ route('petugas.retribusi.store') }}" method="POST" id="retribusiForm">
                @csrf

                <!-- Step 1: Jenis Pembayar -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label class="form-label">Jenis Pembayar <span class="text-danger">*</span></label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="jenis_pembayar" id="pribadi"
                                            value="pribadi" {{ old('jenis_pembayar') == 'pribadi' ? 'checked' : '' }}
                                            required>
                                        <label class="form-check-label" for="pribadi">
                                            <strong>Pribadi</strong><br>
                                            <small class="text-muted">Pembayaran atas nama pribadi</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="jenis_pembayar" id="perusahaan"
                                            value="perusahaan" {{ old('jenis_pembayar') == 'perusahaan' ? 'checked' : '' }}
                                            required>
                                        <label class="form-check-label" for="perusahaan">
                                            <strong>Perusahaan</strong><br>
                                            <small class="text-muted">Pembayaran atas nama perusahaan</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            @error('jenis_pembayar')
                                <div class="text-danger">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Step 2: Company Selection (only for perusahaan) -->
                <div class="row" id="companySection" style="display: none;">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="company_id" class="form-label">Pilih Perusahaan <span
                                    class="text-danger">*</span></label>
                            <select class="form-select @error('company_id') is-invalid @enderror" id="company_id"
                                name="company_id">
                                <option value="">Pilih Perusahaan</option>
                                @foreach ($companies as $company)
                                    <option value="{{ $company->id }}"
                                        {{ old('company_id') == $company->id ? 'selected' : '' }}>
                                        {{ $company->nama_perusahaan }}
                                    </option>
                                @endforeach
                            </select>
                            @error('company_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Step 3: Kapal Selection -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="kapal_id" class="form-label">Pilih Kapal <span class="text-danger">*</span></label>
                            <select class="form-select @error('kapal_id') is-invalid @enderror" id="kapal_id"
                                name="kapal_id" required>
                                <option value="">Pilih jenis pembayar terlebih dahulu</option>
                            </select>
                            @error('kapal_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Step 4: Tarif Selection -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="tarif_id" class="form-label">Pilih Tipe Tarif <span
                                    class="text-danger">*</span></label>
                            <select class="form-select @error('tarif_id') is-invalid @enderror" id="tarif_id"
                                name="tarif_id" required>
                                <option value="">Pilih Kapal Terlebih Dahulu</option>
                            </select>
                            @error('tarif_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">
                                <i class="ti ti-info-circle me-1"></i>
                                Pilihan tarif akan disesuaikan dengan kategori kapal yang dipilih
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="jumlah" class="form-label">Jumlah <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="number" step="0.01" min="0.01"
                                    class="form-control @error('jumlah') is-invalid @enderror" id="jumlah"
                                    name="jumlah" value="{{ old('jumlah') }}" required>
                                <span class="input-group-text" id="satuanText">-</span>
                            </div>
                            @error('jumlah')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Calculation Display -->
                <div class="row" id="calculationSection" style="display: none;">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <h6 class="mb-2">Perhitungan:</h6>
                            <p class="mb-1">Harga per <span id="displaySatuan">-</span>: <span
                                    id="displayHarga">-</span></p>
                            <p class="mb-1">Jumlah: <span id="displayJumlah">-</span> <span
                                    id="displaySatuan2">-</span></p>
                            <hr>
                            <h5 class="mb-0">Total: <span id="displayTotal" class="text-primary">-</span></h5>
                        </div>
                    </div>
                </div>

                <!-- Step 5: Payment Method -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="metode_pembayaran_id" class="form-label">Metode Pembayaran <span
                                    class="text-danger">*</span></label>
                            <select class="form-select @error('metode_pembayaran_id') is-invalid @enderror"
                                id="metode_pembayaran_id" name="metode_pembayaran_id" required>
                                <option value="">Pilih Metode Pembayaran</option>
                                @foreach ($metodePembayarans as $metode)
                                    <option value="{{ $metode->id }}"
                                        {{ old('metode_pembayaran_id') == $metode->id ? 'selected' : '' }}>
                                        {{ $metode->nama_metode }} ({{ $metode->tipe_metode_label }})
                                    </option>
                                @endforeach
                            </select>
                            @error('metode_pembayaran_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Keterangan -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label for="keterangan" class="form-label">Keterangan</label>
                            <textarea class="form-control @error('keterangan') is-invalid @enderror" id="keterangan" name="keterangan"
                                rows="3">{{ old('keterangan') }}</textarea>
                            @error('keterangan')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="ti ti-device-floppy me-2"></i>Buat Retribusi
                    </button>
                    <a href="{{ route('petugas.dashboard') }}" class="btn btn-outline-secondary">
                        <i class="ti ti-arrow-left me-2"></i>Kembali
                    </a>
                </div>
            </form>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            // Handle jenis pembayar change
            $('input[name="jenis_pembayar"]').change(function() {
                const jenisPembayar = $(this).val();

                if (jenisPembayar === 'perusahaan') {
                    $('#companySection').show();
                    $('#company_id').prop('required', true);
                } else {
                    $('#companySection').hide();
                    $('#company_id').prop('required', false);
                    $('#company_id').val('');
                }

                // Load ships based on payment type
                loadShipsByPaymentType();
            });

            // Handle company selection change
            $('#company_id').change(function() {
                loadShipsByPaymentType();
            });

            // Handle ship selection change
            $('#kapal_id').change(function() {
                loadTarifsByShipCategory();
            });

            // Function to load ships based on payment type and company
            function loadShipsByPaymentType() {
                const jenisPembayar = $('input[name="jenis_pembayar"]:checked').val();
                const companyId = $('#company_id').val();

                if (!jenisPembayar) {
                    return;
                }

                // Clear current ship options
                $('#kapal_id').html('<option value="">Loading...</option>');

                // Prepare data for AJAX request
                const requestData = {
                    jenis_pembayar: jenisPembayar
                };

                if (jenisPembayar === 'perusahaan' && companyId) {
                    requestData.company_id = companyId;
                }

                // Make AJAX request to get ships
                $.ajax({
                    url: '/petugas-dashboard/ships-by-payment-type',
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    data: requestData,
                    success: function(response, textStatus, xhr) {
                        // Check if response is actually HTML (login page redirect)
                        if (typeof response === 'string' && response.includes('<html')) {
                            // Session expired, redirect to login
                            alert('Sesi Anda telah berakhir. Silakan login kembali.');
                            window.location.href = '{{ route('petugas.login') }}';
                            return;
                        }

                        // Check if response is an array
                        if (!Array.isArray(response)) {
                            console.error('Invalid response format:', response);
                            $('#kapal_id').html(
                                '<option value="">Error: Invalid response format</option>');
                            return;
                        }

                        let options = '<option value="">Pilih Kapal</option>';

                        response.forEach(function(ship) {
                            const kategoriText = ship.kategori_nama ?
                                ` - ${ship.kategori_nama}` : ' - Tanpa Kategori';
                            options +=
                                `<option value="${ship.id}">${ship.nama_kapal} (${ship.nomor_imo})${kategoriText}</option>`;
                        });

                        $('#kapal_id').html(options);
                    },
                    error: function(xhr, status, error) {
                        $('#kapal_id').html('<option value="">Error loading ships</option>');
                        console.error('Error loading ships:', error);
                        console.error('Response:', xhr.responseText);

                        // Handle authentication errors
                        if (xhr.status === 401 || xhr.status === 403) {
                            try {
                                const response = JSON.parse(xhr.responseText);
                                alert(response.message ||
                                    'Sesi Anda telah berakhir. Silakan login kembali.');
                            } catch (e) {
                                alert('Sesi Anda telah berakhir. Silakan login kembali.');
                            }
                            window.location.href = '{{ route('petugas.login') }}';
                            return;
                        }

                        // Check if it's a redirect to login page (302 or response contains login form)
                        if (xhr.status === 302 || (xhr.responseText && xhr.responseText.includes(
                                'petugas-login'))) {
                            alert('Sesi Anda telah berakhir. Silakan login kembali.');
                            window.location.href = '{{ route('petugas.login') }}';
                            return;
                        }
                    }
                });
            }

            // Function to load tarifs based on selected ship category
            function loadTarifsByShipCategory() {
                const kapalId = $('#kapal_id').val();

                if (!kapalId) {
                    // Clear tarif options if no ship selected
                    $('#tarif_id').html('<option value="">Pilih Tipe Tarif</option>');
                    $('#calculationSection').hide();
                    return;
                }

                // Clear current tarif options
                $('#tarif_id').html('<option value="">Loading...</option>');

                // Make AJAX request to get tarifs based on ship category

                $.ajax({
                    url: '/petugas-dashboard/tarifs-by-ship-category',
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    data: {
                        kapal_id: kapalId
                    },
                    success: function(response, textStatus, xhr) {
                        // Check if response is actually HTML (login page redirect)
                        if (typeof response === 'string' && response.includes('<html')) {
                            // Session expired, redirect to login
                            alert('Sesi Anda telah berakhir. Silakan login kembali.');
                            window.location.href = '{{ route('petugas.login') }}';
                            return;
                        }

                        // Check if response is an array
                        if (!Array.isArray(response)) {
                            console.error('Invalid response format:', response);
                            $('#tarif_id').html(
                                '<option value="">Error: Invalid response format</option>');
                            return;
                        }

                        let options = '<option value="">Pilih Tipe Tarif</option>';

                        response.forEach(function(tarif) {
                            options += `<option value="${tarif.id}" data-harga="${tarif.harga}" data-satuan="${tarif.satuan}">
                                ${tarif.display_text}
                            </option>`;
                        });

                        $('#tarif_id').html(options);

                        // Hide calculation section when tarifs are reloaded
                        $('#calculationSection').hide();
                    },
                    error: function(xhr, status, error) {
                        $('#tarif_id').html('<option value="">Error loading tarifs</option>');
                        console.error('Error loading tarifs:', error);
                        console.error('Response:', xhr.responseText);

                        // Handle authentication errors first
                        if (xhr.status === 401 || xhr.status === 403) {
                            try {
                                const response = JSON.parse(xhr.responseText);
                                alert(response.message ||
                                    'Sesi Anda telah berakhir. Silakan login kembali.');
                            } catch (e) {
                                alert('Sesi Anda telah berakhir. Silakan login kembali.');
                            }
                            window.location.href = '{{ route('petugas.login') }}';
                            return;
                        }

                        // Check if it's a redirect to login page (302 or response contains login form)
                        if (xhr.status === 302 || (xhr.responseText && xhr.responseText.includes(
                                'petugas-login'))) {
                            alert('Sesi Anda telah berakhir. Silakan login kembali.');
                            window.location.href = '{{ route('petugas.login') }}';
                            return;
                        }

                        // Show user-friendly error message
                        if (xhr.status === 500) {
                            alert(
                                'Terjadi kesalahan server saat memuat data tarif. Silakan coba lagi atau hubungi administrator.'
                            );
                        } else if (xhr.status === 404) {
                            alert(
                                'Endpoint untuk memuat tarif tidak ditemukan. Silakan hubungi administrator.'
                            );
                        } else {
                            alert('Gagal memuat data tarif. Silakan coba lagi.');
                        }
                    }
                });
            }

            // Handle tarif change
            $('#tarif_id').change(function() {
                const selectedOption = $(this).find('option:selected');
                const harga = selectedOption.data('harga');
                const satuan = selectedOption.data('satuan');

                if (harga && satuan) {
                    $('#satuanText').text(satuan);
                    $('#displayHarga').text('Rp ' + new Intl.NumberFormat('id-ID').format(harga));
                    $('#displaySatuan, #displaySatuan2').text(satuan);
                    calculateTotal();
                } else {
                    $('#satuanText').text('-');
                    $('#calculationSection').hide();
                }
            });

            // Handle jumlah change
            $('#jumlah').on('input', function() {
                calculateTotal();
            });

            function calculateTotal() {
                const selectedOption = $('#tarif_id').find('option:selected');
                const harga = selectedOption.data('harga');
                const jumlah = parseFloat($('#jumlah').val()) || 0;

                if (harga && jumlah > 0) {
                    const total = harga * jumlah;
                    $('#displayJumlah').text(jumlah);
                    $('#displayTotal').text('Rp ' + new Intl.NumberFormat('id-ID').format(total));
                    $('#calculationSection').show();
                } else {
                    $('#calculationSection').hide();
                }
            }

            // Initialize on page load
            if ($('input[name="jenis_pembayar"]:checked').val() === 'perusahaan') {
                $('#companySection').show();
            }

            if ($('#tarif_id').val()) {
                $('#tarif_id').trigger('change');
            }

            // Load ships on initial page load if payment type is already selected
            const initialPaymentType = $('input[name="jenis_pembayar"]:checked').val();
            if (initialPaymentType) {
                loadShipsByPaymentType();
            }
        });
    </script>
@endsection
